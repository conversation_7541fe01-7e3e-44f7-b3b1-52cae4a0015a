/**index.wxss**/
page{
    height: 100%;
}

.container {
    height: 100%;
    width: 100%;
    background-color: #fff;
}

.text-content {
    width: 100%;
    height: 38vh;
    background: #e5f2ff;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
}

.langs-container{
    display: flex;
    flex-direction: row;
    height: 170rpx;
    padding: 40rpx 0 0 40rpx;
}

.tabs-line-choose{
    background: #99ccff !important;
    height: 2rpx !important;
}

.hr-container{
    display: flex;
    justify-content: center;
    align-items: center;
}

.horizon{
    width: 90%;
    height: 2rpx;
    background-color: #f2f2f2;
}

.active {
    color: #fff;
    background: #4990E2;
}


.all-img{
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100% - 38vh - 150rpx);
}

.play-img {
    width: 180rpx;
    height: 180rpx;
}

.lang-blocks-container{
    display: flex;
    margin-left: 5%;
    margin-right: 30%;
    margin-top: 20rpx;
    /* justify-content: space-between; */
}

.lang-block{
    /* height: 37rpx;
    width: 100rpx; */
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    border-radius: 20rpx;
    color: #ccc;
    padding: 20rpx;
}

.lang-block-active{
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #66b2ff;
    border-radius: 20rpx;
    color: #66b2ff;
    padding: 20rpx;
}

