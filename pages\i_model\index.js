// pages/i_model/index.js
const app = getApp()
var common = require("./../../dist/js/common.js")
Page({
    /**
     * 页面的初始数据
     */
    data: {
        switchPage: false,
        model: {
            name: "",
            gender: "0",
        },
        modelGnderRules: {
            required: true,
            message: "请选择性别",
            trigger: "change"
        },
        modelNameRules: [{
                required: true,
                message: "请输入模型名称",
                trigger: "change"
            },
            {
                min: 2,
                max: 15,
                message: "名称长度在2-15个字符之间",
                trigger: "change"
            },
            {
                pattern: '^[A-Za-z0-9]+$',
                message: '名称必须由数字字母组成',
                trigger: 'change'
            }
        ]
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 使用linui表单需要初始化
        wx.lin.initValidateForm(this)
    },

    
    onModelFormSubmit(event) {
        const { detail } = event;
        if (detail.isValidate == false) {  // 表单验证没通过就return
            return
        }
        wx.showLoading({
          title: '加载中',
        })
        const { modelGender, modelName } = detail.values;
        common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/wechat/query_model_all", {}, {
            token: app.globalData.userInfo.token
        }, "GET").then(res => {
            wx.hideLoading({
                success: (res) => {},
            })
            let existTag = false
            // 响应成功
            if (res.data.state = "success"){
                // 已可用的模型
                res.data.useable_models.forEach((item, index, err) => {
                    console.log("item: ", item)
                    if (item.model_name == modelName ){
                        existTag = true
                        wx.showToast({
                           title: '模型名称已存在',
                           icon: "error",
                           duration: 2000
                        })
                    }
                })
                // 正在训练的模型
                res.data.training_models.forEach((item, index, err) => {
                    console.log("item: ", item)
                    if (item.model_name == modelName ){
                        existTag = true
                        wx.showToast({
                           title: '模型名称已存在',
                           icon: "error",
                           duration: 2000
                        })
                    }
                })
            }
            if (!existTag) {  // 如果名称合法
                common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/wechat/edit_model", { new_gender:modelGender, new_name: modelName }, {
                    token: app.globalData.userInfo.token
                }, "GET").then(res => {
                    if (res.statusCode == 200){
                        if (res.data.state == "success") {
                              // 切换页面
                              this.setData({
                                switchPage: true
                              })
                        }
                        else {
                            wx.showToast({
                              title: "模型无法再次更新，将退出当前页面",
                              icon: "none",
                              duration: 2000
                            })
                            // 这里预防一下，如果要重复更新就跳转到别的地方
                            wx.redirectTo({
                              url: "../i_tts/index",
                            })
                        }
                    }
                    // 这里如果有异常 应该会被外面一层的catch捕捉到 所以这里不用重复写catch应该可以
                })
            }
        }).catch(err => {
            console.log(err)
            // 服务异常就不跳转
            wx.hideLoading({
                success: (res) => {},
            })
            wx.showToast({
                title: '服务异常',
                duration: 2000
            })
        })
    },

    onRedirectIndex() {
        wx.switchTab({
          url: '../tts/index',
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})