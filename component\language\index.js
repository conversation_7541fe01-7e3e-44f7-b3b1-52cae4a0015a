// component/language/index.js
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        name:String,
        tag:String,  // [train, xvector]
        activeName:String,  // 监听参数 模型名称
        activeTag:String,  // 监听参数 区分模型类别
        icon: {
            type:String,
            value:"user" // 未传值时的默认值
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        // 组件是否激活标识(被点击)
        active:false
    },

    //数据监听器
    observers: {
        "activeName, activeTag": function(activeName, activeTag){
            if (activeName == this.data.name && activeTag == this.data.tag){
                this.setData({
                    active: true
                })
            }
            else{
                this.setData({
                    active: false
                })
            }
        }

    },

    /**
     * 组件的方法列表
     */
    methods: {
    }
})
