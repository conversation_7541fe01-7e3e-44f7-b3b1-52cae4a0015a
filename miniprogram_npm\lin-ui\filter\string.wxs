var is = require("./is.wxs");

function _isString(targetString, funName) {
    if (!is.isString(targetString)) {
        console.log('[WXS:String]' + funName + ',targetString must be string');
        return false;
    }
    return true;
}
/**
 * toString 返回指定对象的字符串形式
 * @param {String} targetString 目标字符串
 */

function toString(targetString) {
    return targetString && targetString.toString();
}

/**
 * valueOf 返回指定对象的value
 * @param {String} targetString 目标字符串
 */

function valueOf(targetString) {
    return targetString && targetString.valueOf();
}

/**
 * charAt 一个字符串中返回指定的字符。
 * @param {String} targetString 目标字符串
 * @param {Number} index 返回指定字符的位置
 *
 */
function charAt(targetString, index = 0) {
    var validatorString = _isString(targetString, 'charAt');
    if (validatorString) return targetString.charAt(index);
}

/**
 * charCodeAt 返回0到65535之间的整数，表示给定索引处的UTF-16代码单元
 * @param {String} targetString 目标字符串
 * @param {Number} index 返回指定字符的位置
 *
 */
function charCodeAt(targetString, index = 0) {
    var validatorString = _isString(targetString, 'charCodeAt');
    if (validatorString) return targetString.charCodeAt(index);
}

/**
 * indexOf 查找字符串
 * @param {String} targetString 目标字符串
 * @param {String} searchValue 被查找的值
 * @param {Number} fromIndex 开始查找的位置
 *
 */
function indexOf(targetString, searchValue, fromIndex = 0) {
    var validatorString = _isString(targetString, 'indexOf');
    if (validatorString) return targetString.indexOf(searchValue, fromIndex);
}

/**
 * lastIndexOf 查找字符串最后出现的位置
 * @param {String} targetString 目标字符串
 * @param {String} searchValue 被查找的值
 * @param {Number} fromIndex 开始查找的位置，str.length
 *
 */
function lastIndexOf(targetString, searchValue, fromIndex = 0) {
    var validatorString = _isString(targetString, 'lastIndexOf');
    if (validatorString) return targetString.lastIndexOf(searchValue, fromIndex || targetString.length);
}

/**
 * slice 取一个字符串的一部分，并返回一新的字符串
 * @param {String} targetString 目标字符串
 * @param {Number} beginSlice 从该索引（以 0 为基数）处开始提取原字符串中的字符
 * @param {Number} endSlice 在该索引（以 0 为基数）处结束提取字符
 *
 */
function slice(targetString, beginSlice, endSlice) {
    var validatorString = _isString(targetString, 'slice');
    if (validatorString) return targetString.slice(beginSlice, endSlice || targetString.length);
}

/**
 * split 分割字符串
 * @param {String} targetString 目标字符串
 * @param {String} separator 从该索引（以 0 为基数）处开始提取原字符串中的字符
 * @param {Number} limit
 *
 */
function split(targetString, separator, limit) {
    var validatorString = _isString(targetString, 'split');
    if (validatorString) return targetString.split(separator, limit);
}

/**
 * substring 取一个字符串的一部分，并返回一新的字符串
 * @param {String} targetString 目标字符串
 * @param {Number} indexStart 需要截取的第一个字符的索引，该字符作为返回的字符串的首字母。
 * @param {Number} indexEnd 一个 0 到字符串长度之间的整数，以该数字为索引的字符不包含在截取的字符串内。
 *
 */
function substring(targetString, indexStart, indexEnd) {
    var validatorString = _isString(targetString, 'substring');
    if (validatorString) return targetString.substring(indexStart, indexEnd);
}

/**
 * toLowerCase 字符串值转为小写形式
 * @param {String} targetString 目标字符串
 *
 */
function toLowerCase(targetString) {
    var validatorString = _isString(targetString, 'toLowerCase');
    if (validatorString) return targetString.toLowerCase();
}

/**
 * toLocaleLowerCase 字符串值转为小写形式
 * @param {String} targetString 目标字符串
 *
 */
function toLocaleLowerCase(targetString) {
    var validatorString = _isString(targetString, 'toLocaleLowerCase');
    if (validatorString) return targetString.toLocaleLowerCase();
}

/**
 * toUpperCase 字符串值转为大写形式
 * @param {String} targetString 目标字符串
 *
 */
function toUpperCase(targetString) {
    var validatorString = _isString(targetString, 'toUpperCase');
    if (validatorString) return targetString.toUpperCase();
}

/**
 * toLocaleUpperCase 根据任何特定于语言环境的案例映射，返回调用字符串值转换为大写的值。
 * @param {String} targetString 目标字符串
 *
 */
function toLocaleUpperCase(targetString) {
    var validatorString = _isString(targetString, 'toLocaleUpperCase');
    if (validatorString) return targetString.toLocaleUpperCase();
}

/**
 * trim 从一个字符串的两端删除空白字符
 * @param {String} targetString 目标字符串
 *
 */
function trim(targetString) {
    var validatorString = _isString(targetString, 'trim');
    if (validatorString) return targetString.trim();
}




module.exports = {
    indexOf: indexOf,
    charAt: charAt,
    charCodeAt: charCodeAt,
    toString: toString,
    valueOf: valueOf,
    lastIndexOf: lastIndexOf,
    slice: slice,
    split: split,
    substring: substring,
    toLowerCase: toLowerCase,
    toLocaleLowerCase: toLocaleLowerCase,
    toUpperCase: toUpperCase,
    toLocaleUpperCase: toLocaleUpperCase,
    trim: trim
}
