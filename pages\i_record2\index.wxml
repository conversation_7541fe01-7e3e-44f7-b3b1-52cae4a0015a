<!-- Xvector的语音录制，只需要录制一条语音 -->
<view class="container">
    <view class="serial-container">
            <text>1</text>
    </view>
    <view class="text-container">
        <text>{{textcontent}}</text>
    </view>

    <image src="../../images/luyin.gif" bindtap='startRecord' class="center-img" wx-if="{{isluyin}}" />

    <view class="{{!isluyin?'bofanimgtwo':'bofanimgtwo on'}}" bindtouchstart="startRecord" bindtouchend="stopRecord">
    </view>
    <text class="tip">按住麦克风按钮，然后开始朗读</text>
</view>