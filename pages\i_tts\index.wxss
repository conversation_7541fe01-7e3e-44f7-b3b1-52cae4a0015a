/* pages/tts.wxss */
page{
    height: 100%;
    width: 100%;
    background: #fff;
}

.container {
    height: 100%;
    width: 100%;
    background-color: #fff;
}

.langs-container{
    display: flex;
    flex-direction: row;
    height: 15%;
    width: 100%;
    padding: 40rpx 0 0 40rpx;
}

/* 训练中的模型图标的样式 */
.load-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 180rpx;
}

.load-avatar{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 100rpx;
    margin-right: 80rpx;  /*子元素要margin-right撑开, 否则会有一部分被scoll-view右边隐藏*/
    background-color: #e0e5eb;
    border-radius:50%
}

.load-name{
    font-size: 30rpx;
    height: 80rpx;
    margin-right: 80rpx;  /*子元素要margin-right撑开*/
    color:#c1cbd7 !important;
}

.load-name text{  /*load-name 里的 text标签样式*/
    line-height: 80rpx;
}

.hr-container{
    display: flex;
    justify-content: center;
    align-items: center;
}

.horizon{
    width: 90%;
    height: 2rpx;
    background-color: #f2f2f2;
}

.storys-container{
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 75%;
    padding: 0 30rpx 0 30rpx;  /*上右下左*/
    font-size: 27rpx;
}


.story-content{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 10rpx;
    background-color: #e5f2ff;
    height: 150rpx;
    border-radius:10rpx;
    padding: 0 15rpx 0 15rpx;  /*上右下左*/
}

