function ajaxCommon(url, data, options, method) {
    return new Promise((resolve, reject) => {
        wx.request({
            url: url,
            header: {
                "Content-Type": "application/x-www-form-urlencoded",  // 小程序的wx.request用multipart/form-data没用，后端解析不了，用application/x-www-form-urlencoded可以。
                "Authorization": "Bearer " + options.token // 后端通过 Bearer Auth 来验证
            },
            data: data,
            method: method,
            success: function (res) {
                if (res.statusCode == 401) {
                    loginAndToast()  // 没权限的请求跳转登录
                }
                if (res.statusCode == 500) {
                    wx.showToast({
                        title: '服务异常，请稍后重试。',
                        duration: 2000,
                        icon: 'error'
                    })
                }
                resolve(res)
            },
            fail: function (err) {
                // wx.showToast({
                //     title: '请求异常。',
                //     duration: 2000,
                //     icon: "none",
                // })
                reject(err)
            }
        })
    })
}

function getUserInfo(url, data, options, method) {
    return new Promise((resolve, reject) => {
        wx.request({
            url,
            data: data,
            method: method,
            header: {
                "Content-Type": "multipart/form-data",
                "Authorization": "Bearer " + options.token // 后端通过 Bearer Auth
            },
            success: function (res) {
                if (res.statusCode == 401) {
                    loginAndToast()
                }
                resolve(res)
            },
            fail: function (err) {
                wx.showToast({
                    title: '请求异常。',
                    duration: 1500,
                    icon: "none",
                })
                reject(err)
            }
        })
    })
}

function taskStateMap(state_code) {
    let state_string = ""
    switch (state_code) {
        case -1:
            state_string = "上传文件数量不足"
            break;
        case 0:
            state_string = "任务等待训练"
            break;
        case 1:
            state_string = "任务开始排队"
            break;
        case 2:
            state_string = "任务正在训练"
            break;
        case 3:
            state_string = "任务训练完成"
            break;
        case -2:
            state_string = "任务训练失败"
            break;
        default:
            state_string = "未知状态码" + state_string.toString()
    }
    return state_string
}

function ajaxUploadFile(url, FilePaths, formData, options, fn) {
    return new Promise((resolve, reject) => {
        wx.uploadFile({
            url: url,
            filePath: FilePaths,
            name: 'file',
            formData: formData,
            header: {
                "Content-Type": "multipart/form-data",
                "Authorization": "Bearer " + options.token // 后端通过 Bearer Auth
            },
            success: res => {
                //   fn && fn(res.data);
                if (res.statusCode == 401) {
                    loginAndToast()
                }
                resolve(res)
            },
            fail: err => {
                wx.showToast({
                    title: '系统故障，请稍后重试',
                    duration: 1500,
                    icon: "none",
                })
                reject(err)
            }
        })
    })
}

function loginAndToast() {
    wx.showToast({
        title: '请先登录',
        duration: 2000,
        icon: "none",
        success: function () {
            setTimeout(function () {
                wx.navigateTo({
                    url: '/pages/login/index',
                })
            }, 1500) //延迟时间
        }
    })
}

module.exports = {
    getUserInfo,
    ajaxCommon,
    loginAndToast,
    ajaxUploadFile,
    taskStateMap
}