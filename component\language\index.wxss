/* component/language/index.wxss */
.lang-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 80rpx;
}

.lang-nickname{
    margin-top: 0 !important;
    font-size: 30rpx;
    color:#c1cbd7 !important;
}

.lang-nickname-active{
    margin-top: 0 !important;
    font-size: 30rpx;
    color: #66b2ff !important;
}

.lang-avatar-active{
    background-color: #66b2ff !important; /*图标背景*/
}

.lang-avatar{
    background-color: #e0e5eb !important;
}

/* 2022-7-29新增 */
.xvector-lang-nickname-active{
    margin-top: 0 !important;
    font-size: 30rpx;
    color: #66edff !important;
}

.xvector-lang-avatar-active{
    background-color: #66edff !important; /*图标背景*/
}