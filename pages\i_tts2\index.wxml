<view class="container">
    <scroll-view enable-flex scroll-x class="langs-container">
        <block wx:for="{{usableModels}}" wx:key="index">
            <language name="{{item}}" tag="train" bindtap="onChangeActive" data-name="{{item}}" activeName="{{currentActiveName}}" data-tag= "train" activeTag="{{currentActiveTag}}" />
        </block>
        <block wx:for="{{xvectorModels}}" wx:key="index">
            <language name="{{item}}" tag="xvector" bindtap="onChangeActive" data-name="{{item}}" activeName="{{currentActiveName}}" data-tag= "xvector" activeTag="{{currentActiveTag}}" />
        </block>
    </scroll-view>
    <l-textarea placeholder="请输入文本内容" value="{{textcontent}}" border="{{false}}" disabled="{{true}}" l-class="text-content" indicator="{{false}}" maxlength="-1" />
    <!-- <l-progress percent="{{ progressBarPercentage }}" l-class="progress-bar"></l-progress> -->
    <view class="play-bar">
        <image src="../../images/pre.png" style="width: 90rpx;height: 90rpx;" bindtap="switchPreStory" />
        <!-- <l-icon name="left" size="50" bindtap="switchPreStory"/> -->
        <!-- 未播放状态 -->
        <image src="../../images/stop.png" class="play-img" bindtap='playStart' wx-if="{{audioStatus=='stop'}}" />
        <!-- 播放状态 -->
        <image src="../../images/play.png" class="play-img" bindtap='playPaused' wx-if="{{audioStatus=='playing'}}" />
        <!-- 暂停状态未播放 -->
        <image src="../../images/stop.png" class="play-img" bindtap='playGoOn' wx-if="{{audioStatus=='paused'}}" />
        <!-- <l-icon name="right" size="50" bindtap="switchNexStory" /> -->
        <image src="../../images/next.png" style="width: 90rpx;height: 90rpx;" bindtap="switchNexStory" />
    </view>

</view>