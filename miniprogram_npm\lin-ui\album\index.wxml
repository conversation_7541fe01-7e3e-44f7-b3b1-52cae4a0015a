<wxs src="index.wxs" module="album"></wxs>

<view class="l-class" style="display:flex">
  <view style="{{album.gridStyle(gapRow, gapColumn, multipleSize, everyRowNumber)}}">
    <block wx:for="{{showUrls}}" wx:key="index" class="child">
      <view class="imageContainer {{album.dimBack(isLong, index, maxNumber, previewFullImage)}}" style="{{album.blockStyle(urls, horizontalScreen, shortSideValue, singleSize, multipleSize)}}">
        <image class="{{album.blockClass(urls, horizontalScreen) + ' ' + album.dim(isLong, index, maxNumber, previewFullImage)}}" id="{{index}}" bind:tap="onPreviewTap" style="{{album.blockStyle(urls, horizontalScreen, shortSideValue, singleSize, multipleSize)}}" src="{{newType?item[key]:item}}" mode="{{urls.length === 1?singleMode:multipleMode}}"/>
        <view wx:if="{{album.image(isLong, index, maxNumber, previewFullImage)}}" class="text">+{{urls.length-maxNumber}}</view>
      </view>
    </block>
  </view>
</view>
