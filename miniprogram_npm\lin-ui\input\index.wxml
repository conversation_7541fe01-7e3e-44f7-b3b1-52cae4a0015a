
<label class='form-item {{disabled? "disabled": ""}} l-class form-item-{{labelLayout}}' style="width:{{width===null?'auto':width+'rpx'}}">
  <view class="mask" wx:if="{{disabled}}"></view>
  <view class="row l-row-class" hidden="{{ showRow ? '' : 'hidden' }}" style="width:{{width===null?'100%':width+'rpx'}}"></view>
  <view wx:if="{{label && !labelCustom}}" hidden="{{hideLabel}}" class="form-label l-label-class form-label-{{labelLayout}}" style='{{labelLayout !== "top" ? "width:"+ labelWidth+ "rpx;" : "" }} height:{{labelLayout=== "top" ? labelWidth + "rpx" : "" }}'>
    <text><text class="text-require" wx:if="{{required}}">* </text>{{label}}<text wx:if="{{colon}}">：</text>
    </text>
  </view>
  <view wx:else hidden="{{hideLabel}}" class="form-label l-label-class form-label-{{labelLayout}}" style='{{labelLayout !== "top" ? "width:"+ labelWidth+ "rpx;" : "" }} height:{{labelLayout=== "top" ? labelWidth + "rpx" : "" }}'>
    <slot name="left"/>
  </view>
  
  <input wx:if="{{type !== 'password'}}" adjust-position="{{adjustPosition}}" class="input {{hideLabel?'hideLabel':''}} l-input-class" value="{{ value }}" type="{{type}}" placeholder="{{placeholder}}" maxlength="{{maxlength}}" placeholder-class="pls-class" placeholder-style="{{placeholderStyle}}" disabled="{{disabled}}" focus="{{focus}}" bindinput="handleInputChange" bindfocus="handleInputFocus" bindblur="handleInputBlur" bindconfirm="handleInputConfirm"/>
  <input wx:else adjust-position="{{adjustPosition}}" class="input {{hideLabel?'hideLabel':''}} l-input-class" value="{{ value }}" password="{{true}}" placeholder="{{placeholder}}" maxlength="{{maxlength}}" placeholder-class="pls-class" placeholder-style="{{placeholderStyle}}" disabled="{{disabled}}" focus="{{focus}}" bindinput="handleInputChange" bindfocus="handleInputFocus" bindblur="handleInputBlur" bindconfirm="handleInputConfirm"/>
  <l-icon wx:if="{{showEye&&value}}" name="eye" catch:tap="onTapEyeIcon" size="40" l-class="l-eye l-eye-{{type}}"/>
  <view class="close" wx:if="{{clear&&value}}" mut-bind:tap="onClearTap">
    <view class="close-icon">
      <l-icon name="close" color="#fff" size="16"/>
    </view>
  </view>
  <slot name="right"/>
  <l-error-tip l-error-text-class="l-error-text l-error-text-class" errorText="{{errorText}}" wx:if="{{errorText}}"/>
</label>
