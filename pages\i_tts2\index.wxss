/* pages/tts.wxss */
page{
    height: 100%;
    background: #fff;
}

.container {
    height: 100%;
    width: 100%;
    background-color: #fff;
}

.langs-container{
    display: flex;
    flex-direction: row;
    height: 15%;
    padding: 40rpx 0 0 40rpx;
}

.text-content {
    width: 100%;
    height: 470rpx;
    margin-top: 30rpx;
    /* overflow-y: scroll; */
}

.progress-bar {
    padding: 0 30rpx 0 30rpx
}

.play-bar {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    margin: 200rpx 50rpx 0 50rpx;
}

/* .all-img{
    position: fixed;
    left: 38%;
    bottom: 15%;
} */

.play-img {
    width: 130rpx;
    height: 130rpx;
}