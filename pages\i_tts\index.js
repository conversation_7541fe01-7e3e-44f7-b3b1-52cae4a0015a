// pages/tts.js
const app = getApp()
var common = require("../../dist/js/common.js")
var util = require("../../utils/util.js")
Page({
    /**
     * 页面的初始数据
     */
    data: {
        // 语言按键激活标识
        currentActiveName: "",
        // 激活的模型是训练版还是xector版
        currentActiveTag: "",
        // 检索到的可用模型
        usableModels: [],
        // 检索到的正在训练的模型
        trainingModels: [],
        ttsStorys: [],
        showDeleteDialog: false,
        currentDeleteName: "",
        // xvector模型
        xvectorModels: []
    },
    // 点击添加模型按钮 则跳转模型定制页面
    onAddNewModel() {
        wx.switchTab({
          url: "../i_custom_index/index",
        })
    },
    // 长按删除模型
    onDeleteModel(event) {
        this.setData({
            showDeleteDialog: true,
            currentDeleteName: event.currentTarget.dataset.name
        })
    },
    // 询问框确认删除按钮
    onDeleteConfirm(){
        wx.showLoading({
          title: '删除中',
        })
        let url = "https://tc.talentedsoft.com:58123/individuation/api/delete_model"  // train
        if (this.data.currentActiveTag == "xvector") {
            url = "https://tc.talentedsoft.com:58123/individuation/api/xvector/delete_model"
        }
        common.ajaxCommon(url, {
            model_name: this.data.currentDeleteName
        }, 
        {
            token: app.globalData.userInfo.token
        }, "POST").then(res => {
            if(res.statusCode == 200) {
                if (res.data.state == "success") {
                    wx.showToast({
                      title: "模型删除成功",
                      icon: "success",
                      duration: 2000
                    })
                }
                else {
                    wx.showToast({
                      title: "模型删除失败",
                      icon: "none",
                      duration: 2000
                    })
                }
            }
        }).catch(err => {
            console.log(err)
            wx.showToast({
                title: " 服务未响应, 加载模型失败",
                icon: "none",
                duration: 2000
              })
        })
        
        wx.hideLoading({
            success: (res) => {
                this.onShow()
            },
        })

    },
    onNavigate2itts2(event){
        if (this.data.usableModels.length == 0  && this.data.xvectorModels.length == 0){
            wx.showToast({
                title: "暂无可用模型",
                icon: "none",
                duration: 2000
              })
            return
        }
        const dataset = event.currentTarget.dataset
        console.log(dataset)
        wx.navigateTo({  // 向i_tts2/index页面传参
          url: "../i_tts2/index?" + 
          "contentindex=" + dataset.contentindex + "&" +
          "currentactivename=" + dataset.currentactivename + "&" +
          "currentactivetag=" + dataset.currentactivetag,
        })
    },
    
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.setData({
            ttsStorys: app.globalData.ttsStorys
        })
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        let _this = this
        // 加载模型
        common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/wechat/query_model_all", {}, 
        {
            token: app.globalData.userInfo.token
        }, "GET").then(res => {
            if(res.statusCode == 200) {
                if (res.data.state == "success") {
                    console.log(res)
                    _this.setData({
                        usableModels: res.data.useable_models,
                        trainingModels: res.data.training_models,
                        xvectorModels: res.data.xvector_models,
                    })
                    if (_this.data.usableModels.length != 0){
                        _this.setData({
                            currentActiveName: _this.data.usableModels[0],
                            currentActiveTag: "train"
                        })
                    } else {
                        _this.setData({
                            currentActiveName: _this.data.xvectorModels[0],
                            currentActiveTag: "xvector"
                        })
                    }
                }
            }
        }).catch(err => {
            console.log(err)
            wx.showToast({
                title: " 服务未响应, 训练版模型加载失败",
                icon: "none",
                duration: 2000
              })
        })
    },

    //按键点击(更改点亮的按钮)
    onChangeActive: function (event) {
        this.setData({
            currentActiveName: event.currentTarget.dataset.name,
            currentActiveTag: event.currentTarget.dataset.tag
        })
    }
})