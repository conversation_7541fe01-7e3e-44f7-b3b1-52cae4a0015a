// pages / ruyin / ruyin.js
//获取应用实例
const app = getApp()
var common = require("../../dist/js/common.js")
Page({
    data: {
        isluyin: false,
        isanzhu: true,
        textcontent: "我国东北的小兴安岭，有数不清的树，几百里连成一片，就像绿色的海洋。春天，树木抽出新的枝条，长出嫩绿的叶子。山顶上的积雪融化了，雪水汇成了小溪。"
    },

    loadData() {
        let _this = this;
        _this.recorderManager = wx.getRecorderManager();
        // 注册回调
        _this.recorderManager.onError(err => {
            this.setData({
                isluyin: false
            })
            wx.showToast({
                title: "录音失败",
                icon: "error",
                duration: 2000
            })
        });
        _this.recorderManager.onStop(function (res) { // 录音停止的回调
            _this.setData({
                isluyin: false
            })
            wx.redirectTo({
                url: '../i_model2/index?'+ "tempFilePath=" + res.tempFilePath
              })
        });

    },
    onShow() {
        let _this = this;

        // 检查录音接口是否可用（只询问一次）
        wx.getSetting({
            success(res) {
                if (!res['scope.record']) {
                    // 接口调用询问
                    wx.authorize({
                        scope: 'scope.record',
                        success(res) {
                            _this.loadData();
                        },
                        fail() {
                            wx.openSetting({
                                success: (res) => {
                                    res.authSetting = {
                                        "scope.record": true
                                    }
                                    _this.loadData();
                                }
                            })
                        },
                        complete() {}
                    })
                } else {
                    _this.loadData();
                }
            }
        })
    },

    stopRecord: function () {
        this.recorderManager.stop()
    },

    startRecord() {
        let _this = this;
        // 检查接口是否可用
        wx.getSetting({
            success(res) {
                if (!res['scope.record']) {
                    // 接口调用询问
                    wx.authorize({
                        scope: 'scope.record',
                        success(res) {
                            const options = {
                                sampleRate: 16000,
                                frameSize: 16,
                                numberOfChannels: 1,
                                format: 'wav'
                            }
                            _this.setData({
                                'isluyin': true,
                            })
                            _this.recorderManager.start(options);
                        },
                        fail() {
                            wx.showToast({
                                title: '录音权限未开启',
                                icon: 'error'
                            })
                        },
                        complete() {}
                    })
                }
            }
        })
    },

    /*new code from now on */
    onShareAppMessage: function (res) {
        return {
            title: '天聪智能闽南话识别演示平台',
            path: '/pages/index/index'
        }
    },

    onShareTimeline: function (res) {
        return {
            title: '天聪智能闽南话识别演示平台',
            path: '/pages/index/index'
        }
    }
})