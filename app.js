// app.js
App({
    onLaunch() {
        this.globalData.userInfo.token = wx.getStorageSync('token') || ""  // 看一下缓存中有没有token，具体的验证会在进入音色定制相关的页面进行
    },
    // 全局属性 通过app.globalData获取
    globalData: {
        userInfo: {
            // jwt token
            token: "",
            mobile: "", // 手机号码
            task_state: 0, // 模型训练状态
            upload_file_threshold: 0, // 模型训练需要的文件阈值
            user_state: 0, // 用户是否有模型可以调用
            wav_amount: 0 // 用户已上传的语音数量
        },
        // 用于个性化录音上传的文本
        recordWords: [
            "我国东北的小兴安岭，有数不清的树，几百里连成一片，就像绿色的海洋。",
            "春天，树木抽出新的枝条，长出嫩绿的叶子。山顶上的积雪融化了，雪水汇成了小溪。",
            "河里涨满了春水，一个个木排随着流水往前流淌着，像一支舰队在前进。",
            "夏天，树木长得郁郁葱葱，密密层层的叶枝把森林封的严严实实的，挡住了人们的视线，遮住了蓝蓝的天空。",
            "早晨，雾从山谷里升起，整个森林浸在乳白色的浓雾里，草地上盛开着各种各样的野花，像个美丽的大花坛。",
            "太阳出来了。千万缕金光像利剑一样，穿过树梢，照射在宿舍门前的草地上。",
            "秋天，枫叶火红，松柏苍翠，秋风吹来，落叶在林间飞舞。",
            "冬天，雪花在空中飞舞，树上积满了白雪，地上的雪厚厚的，又松又软，常常高过膝盖。",
            "松鼠有时在枝头散步，它们靠秋天收藏在树洞里边的山果过日子。",
            "小兴安岭是一座巨大的宝库，也是一座美丽的大花园。"
        ],
        ttsStorys: [{
            title:"童话故事",
            content:"有一天，森林里的小动物——小兔子捡到了一本好看的童话书。于是小兔子便把它捡到的童话故事书，交给了这里的村长。村长便把森林里的小动物都召集起来，看起了里面的故事。大家都夸小兔子是好样的。"
        },
        {
            title:"动物聚会",
            content:"一天，小动物们要搞聚会了。小猴子拿出一颗豆子，小猴子把豆子放在桌子上，在阳光的照射下，小豆子活蹦乱跳。大家都鼓掌了。大家问小猴子豆子怎么会跳？小猴子掰开豆子里面有两条虫子，大家突然明白了。"
        },
        {
            title:"月光",
            content:"很久很久以前，在一个森林里住着许多动物，它们生活得有滋有味，丰富多彩。有一天，小鹿和小乌龟在一起玩得特别高兴，忘记了回家。天渐渐地黑了，小鹿对小乌龟说：”小乌龟，怎么办，我们找不到回家的路了。”两个小伙伴急坏了。就在这时它们发现月亮悄悄地爬上了树梢。你看，月亮升起来了，我们可以回家了！于是它们踏着银色的月光回到了家。"
        }],
        multilanguageSpks: [
            {
                nickName: "小樱",
                url: "",
                spkId: "7"
            },
            {
                nickName: "小智",
                url: "",
                spkId: "3"
            },
            {
                nickName: "小雅",
                url: "",
                spkId: "5"
            },
            {
                nickName: "小童",
                url: "",
                spkId: "6"
            },
            {
                nickName: "小漫",
                url: "",
                spkId: "4"
            },
            {
                nickName: "小天",
                url: "",
                spkId: "1"
            },
            {
                nickName: "小果",
                url: "",
                spkId: "2"
            }
        ],
        multilanguageIds: [
            {
                langName: "闽南语",
                langId: "3"
            },
            {
                langName: "普通话",
                langId: "0"
            }
        ]
    }
})