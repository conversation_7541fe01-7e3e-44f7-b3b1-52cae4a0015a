<view class="container">
    <scroll-view enable-flex scroll-x class="langs-container">
        <language name="音色定制" icon="add" activeName="音色定制" tag="train" activeTag= "train" bindtap="onAddNewModel"/>
        <block wx:for="{{usableModels}}" wx:key="index">
            <language name="{{item}}" tag="train" bindtap="onChangeActive" data-name="{{item}}" activeName="{{currentActiveName}}" data-tag= "train" activeTag="{{currentActiveTag}}" bindlongtap="onDeleteModel" />
        </block>
        <block wx:for="{{xvectorModels}}" wx:key="index">
            <language name="{{item}}" tag="xvector" bindtap="onChangeActive" data-name="{{item}}" activeName="{{currentActiveName}}" data-tag= "xvector" activeTag="{{currentActiveTag}}" bindlongtap="onDeleteModel" />
        </block>
        <block wx:for="{{trainingModels}}" wx:key="index">
            <view class="load-container">
                <view class="load-avatar">
                    <l-loading show="{{true}}" type="rotate" color="#c1cbd7" size="mini"></l-loading>
                </view>
                <view class="load-name">
                    <text>{{item}}</text>
                </view>
            </view>
        </block>
    </scroll-view>
    <view class="hr-container">
      <view class="horizon"></view>
    </view>
    <view class="storys-container">
        <block wx:for="{{ttsStorys}}" wx:key="index" >
            <view class="story-container"  data-currentActiveName="{{currentActiveName}}" data-currentActiveTag="{{currentActiveTag}}" data-contentIndex="{{index}}" bindtap="onNavigate2itts2">
                <text class="story-title">精选故事{{index + 1}}</text>
                <view class="story-content">
                    <text style="margin-right: 20rpx;">{{item.title}}</text>
                    <l-icon name="more" size="50" /> <!-- 两个储存属性要传到itts2页面 储存属性只能为小写-->
                </view>
            </view>
        </block>
    </view>

    <l-dialog 
        show="{{showDeleteDialog}}"
        type="confirm"  
        content="是否确认删除模型{{currentDeleteName}}？"
        confirm-text="是"
        confirm-color="#66b2ff"
        cancel-text="否"
        cancel-color="#999"
        bind:linconfirm="onDeleteConfirm" />
</view>