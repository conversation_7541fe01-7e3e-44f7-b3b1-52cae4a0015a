const formatFlags={format:function(t,e){let a;const r={"y+":(e=new Date(e)).getFullYear().toString(),"M+":(e.getMonth()+1).toString(),"d+":e.getDate().toString()};for(let e in r)a=new RegExp("("+e+")").exec(t),a&&(t=t.replace(a[1],1===a[1].length?r[e]:this.padZero(r[e],a[1].length)));return t},padZero(t,e){let a=t;for(let r=0;r<e-t;r++)a="0"+a;return a}};formatFlags.format("yyyy/MM/dd",new Date),formatFlags.format("yyyy-MM-dd",new Date),formatFlags.format("yyyy-M-dd",new Date),formatFlags.format("yyyy-M-d",new Date),formatFlags.format("M-dd",new Date),formatFlags.format("MM-dd",new Date);export default formatFlags;