//获取应用实例
var common = require("./../../utils/util.js")
var log = require("../../dist/js/log.js")
const app = getApp()
Page({
    data: {
        //语音播放上下文
        innerAudioContext: {},
        //语音播放状态
        audioStatus: "stop",
        //文本框默认内容
        textcontent: '厦门是一座美丽的海滨城市。',
        //当前音频链接
        currentAudioUrl: "",
        //多语种场景 角色数据
        multilanguageSpks: null,
        //多语种场景 语种数据
        multilanguageIds: null,
        //播放按键自适应高度
        height: null,
        //语言按键激活标识
        currentActiveName: "小樱",
        //当前多语种SpkId 默认为空
        currentMultilangSpkId: 7,
        //当前多语种语种id 默认为空
        currentMultilangId: 3,
        //是否已经合成播放过音频
        isTTSOnce: false
    },

    //生命周期函数--监听页面加载
    onLoad: function (options) {
        this.initAudioContext()  // 初始化播放器

        wx.showShareMenu({
            withShareTicket: true
        })

        //加载全局数据
        this.setData({
            multilanguageSpks: app.globalData.multilanguageSpks,
            multilanguageIds: app.globalData.multilanguageIds,
            height: (wx.getSystemInfoSync().windowHeight - 190 - 157) * 2
        })
    },

    //文本框监听
    bindTextAreaBlur: function (e) {
        this.setData({
            'textcontent': e.detail.value
        })
    },
    //开始播放
    playStart: function () {
        console.log("点击播放按键")
        wx.showLoading({
            title: '加载中',
        })
        const _this = this
        var textcontent = _this.data.textcontent;
        const innerAudioContext = _this.data.innerAudioContext;

        // 判断未输入
        if (!textcontent) {
            wx.showToast({
                title: '请输入想要进行语音合成的字段',
                duration: 1500,
                icon: 'none'
            })
            return
        }

        //判断字数上限
        if (textcontent.length > 1000) {
            wx.showToast({
                title: '输入的字段不能大于1000字',
                duration: 1700,
                icon: 'none'
            })
            return
        }

        var multilangSpkId = _this.data.currentMultilangSpkId
        var multilangId = _this.data.currentMultilangId
        var url = "https://tctts.talentedsoft.com/dotctts";
        var getWavUrl = "https://tctts.talentedsoft.com/tts/"
        var jsonData = {
            "userid": "tc_wx_yun7",
            "token": "15705951797",
            "content": textcontent,
            "spkid": parseInt(multilangSpkId),
            "lanid": parseInt(multilangId)
        }

        if (jsonData["spkid"] == "3"){
            jsonData["volume"] = 2.5
        }

        wx.request({
            url: url,
            method: "POST",
            data: jsonData,
            header: {
              'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
            },
            success(res) {
                if (res.data.errCode !== "0") {
                    wx.hideLoading({
                        success: (res) => {},
                    })
                    wx.showToast({
                        title: '语音合成异常',
                        duration: 2000,
                        icon: "none"
                    })
                    return
                }
                if (res.data.wavfile) {
                    const src = getWavUrl + res.data.wavfile
                    innerAudioContext.src = src
                    innerAudioContext.play()
                }
            },
            fail(err) {
                log.error(err) // 实时日志，后台可查询
                wx.hideLoading({
                    success: (res) => {},
                })
                wx.showToast({
                    title: '语音合成异常',
                    duration: 2000,
                    icon: "none"
                })
            }
          })
    },

    //按键点击(暂停播放)
    playPaused: function () {
        this.data.innerAudioContext.pause();
    },

    //按键点击(继续播放)
    playGoOn() {
        this.data.innerAudioContext.play();
    },

    // 初始化innerAudioContext（PC版小程序切换图标和场景时有时音频不会改变，可能是bug）
    initAudioContext() {
        var innerAudioContext = wx.createInnerAudioContext();
        innerAudioContext.useWebAudioImplement = true;
        this.setData({
            innerAudioContext: innerAudioContext
        })
        this.registerInnerAudioContext(); // 注册回调函数
    },


    //按键点击(更改点亮的按钮)
    onChangeActive: function (event) {
        this.data.innerAudioContext.stop()
        this.setData({
            currentActiveName: event.currentTarget.dataset.name,
            currentMultilangSpkId: event.currentTarget.dataset.spkid
        })
    },

    //多语种场景 选择语种点击
    onChangeActiveMultilang: function (event) {
        this.data.innerAudioContext.stop()
        this.setData({
            currentMultilangId: event.currentTarget.dataset.langid
        })

    },
    //标签切换(首个按键点亮)
    onTabChange: function (event) {
        this.data.innerAudioContext.stop()

        this.setData({
            currentActiveName: '小天',
            currentLangType: 'multilanguage',

            currentMultilangSpkId: '1',
            currentMultilangId: '0',
        })
    },

    //注册音频
    registerInnerAudioContext: function () {
        let innerAudioContext = this.data.innerAudioContext

        //监听音频播放事件
        innerAudioContext.onPlay(() => {
            // 如果是首次播放，那么置一个标志位
            if (!this.data.isTTSOnce) {
                this.setData({
                    isTTSOnce: true
                })
            }
            console.log('监听播放')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'playing',
            })
        })

        //监听音频自然播放至结束的事件
        innerAudioContext.onEnded((res) => {
            console.log('音频播放结束')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
        });

        //监听音频暂停事件
        innerAudioContext.onPause((res) => {
            console.log('播放暂停')
            this.setData({
                'audioStatus': 'paused',
            })
        })

        //监听音频停止事件
        innerAudioContext.onStop((res) => {
            console.log('播放停止')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
        })

        //监听音频播放错误事件
        innerAudioContext.onError((res) => {
            // IOS在首次进入语音合成页面时，如果播放器没有播放过，
            // 然而直接进行stop，此时会触发onError，
            // 目前未知发生原因，因为播放器已在onLoad进行初始化过了
            // 这里进行一个问题兼容
            // 如果还没播放过音频，那么不给出错误提示(可能导致其它问题)
            console.log("音频播放错误: ", res)
            if (!this.data.isTTSOnce){
                return
            }
            console.log('播放错误', res)
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
            wx.showToast({
                title: '语音播放异常，请重试',
                duration: 2000,
                icon: "none"
            })
        })
    },

    // 监听音频因为受到系统占用而被中断开始事件。
    onAudioInterruptionBegin: function () {
        this.setData({
            'audioStatus': 'stop',
        })
        this.data.innerAudioContext.pause();
    },

    // 监听音频因为受到系统占用而被中断开始事件结束。
    onAudioInterruptionBegin: function () {
        this.data.innerAudioContext.start();
        this.setData({
            'audioStatus': 'playing',
        })
    },

    // 允许分享
    onShareAppMessage: function (res){
        return {
        title: '天聪智能演示平台',
        path: '/pages/tts/index'
        }
    },

    // 允许分享到朋友圈
    onShareTimeline: function (res) {
        return {
        title: '天聪智能演示平台',
        path: '/pages/tts/index'
        }
    }
})