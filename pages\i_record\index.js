// pages / ruyin / ruyin.js
//获取应用实例
const app = getApp()
var common = require("../../dist/js/common.js")
Page({
    data: {
        isluyin: false,
        isanzhu: true,
        textcontent: "我国东北的小兴安岭，有数不清的树。",
        text_count: 0,
        upload_percent: 0,
        model_default_name: "WxDefaultName",
    },

    loadData() {
        let _this = this;
        _this.recorderManager = wx.getRecorderManager();
        // 注册回调
        _this.recorderManager.onError(err => {
            this.setData({
                isluyin: false
            })
            wx.showToast({
                title: "录音失败",
                icon: "error",
                duration: 2000
            })
        });
        _this.recorderManager.onStop(function (res) { // 录音停止的回调
            _this.setData({
                isluyin: false
            })
            wx.showLoading({
                title: '上传文件中',
            })
            const tmp_file_path = res.tempFilePath
            common.ajaxUploadFile('https://tc.talentedsoft.com:58123/individuation/api/score', tmp_file_path, {
                text: _this.data.textcontent
            }, {
                token: app.globalData.userInfo.token
            }).then(res => {
                var data = JSON.parse(res.data)
                if (data.state === "success") {
                    // 如果打分成功 那就可以上传文件了
                    wx.uploadFile({
                        url: "https://tc.talentedsoft.com:58123/individuation/api/upload_single",
                        filePath: tmp_file_path,
                        name: 'file',
                        formData: {
                            model_name: "WxDefaultName",  // 小程序未命名模型默认值
                            text: _this.data.textcontent
                        },
                        header: {
                            "Content-Type": "multipart/form-data",
                            "Authorization": "Bearer " + app.globalData.userInfo.token // 后端通过 Bearer Auth
                        },
                        success: function (res) {
                            if (res.data) {
                                var data = JSON.parse(res.data)
                                if (data.state == "success") {
                                    wx.hideLoading({
                                        success: (res) => {},
                                    })
                                    wx.showToast({
                                        title: "上传文件成功",
                                        icon: "success"
                                    })
                                    _this.onShow() // 重新加载一次数据
                                } else {
                                    wx.hideLoading({
                                        success: (res) => {},
                                    })
                                    wx.showToast({
                                        title: "上传文件失败, 请尝试重试录音。",
                                        icon: "none",
                                        duration: 2000
                                    })
                                }
                            }
                        },
                        fail: err => {
                            console.log(err)
                            wx.hideLoading({
                                success: (res) => {},
                            })
                            wx.showToast({
                                title: "上传文件接口请求异常",
                                icon: "none",
                                duration: 2000
                            })
                        }
                    })
                }
                if (data.state === "error") {
                    wx.hideLoading({
                        success: (res) => {},
                    })
                    wx.showToast({
                        title: data.msg,
                        icon: "none",
                        duration: 2000
                    })
                }
            }).catch(err => {
                wx.hideLoading({
                    success: (res) => {},
                })
                wx.showToast({
                    title: "打分接口请求异常",
                    icon: "none",
                    duration: 2000
                })
            })
        });

    },
    onShow() {
        let _this = this;

        // 检查录音接口是否可用（只询问一次）
        wx.getSetting({
            success(res) {
                if (!res['scope.record']) {
                    // 接口调用询问
                    wx.authorize({
                        scope: 'scope.record',
                        success(res) {
                            _this.loadData();
                        },
                        fail() {
                            wx.openSetting({
                                success: (res) => {
                                    res.authSetting = {
                                        "scope.record": true
                                    }
                                    _this.loadData();
                                }
                            })
                        },
                        complete() {}
                    })
                } else {
                    _this.loadData();
                }
            }
        })

        // https://tc.talentedsoft.com:58123/individuation/util/query_info
        // 获取页面需要展示的实时信息
        common.getUserInfo("https://tc.talentedsoft.com:58123/individuation/util/wechat/query_model", { model_name: "WxDefaultName"}, {
            token: app.globalData.userInfo.token
        }, "GET").then(res => {
            const msg = res.data.msg
            const percent = msg.wav_amount / msg.upload_file_threshold * 100
            _this.setData({
                task_state: common.taskStateMap(msg.task_state),
                upload_percent: percent > 100 ? 100 : percent // 大于100就显示100，小于100 就显示原始值
            })
            wx.showShareMenu({
                withShare: true
            })
            // console.log(msg.wav_amount%app.globalData.recordWords.length, app.globalData.recordWords.length, msg.wav_amount)
            // const ind = Math.floor((Math.random() * app.globalData.recordWords.length));
            _this.setData({
                // wav_amount 求余 数组长度，固定文本出现的顺序
                textcontent: app.globalData.recordWords[msg.wav_amount%app.globalData.recordWords.length],
                text_count: msg.wav_amount%app.globalData.recordWords.length + 1
            })
            // 如果提交的文件已满足数量，那么显示对模型命名的页面
            if (this.data.upload_percent == 100) {
                wx.redirectTo({
                  url: '../i_model/index',
                })
            }
        }).catch(err => {
            console.log(err)
        })
    },

    stopRecord: function () {
        this.recorderManager.stop()
    },

    startRecord() {
        let _this = this;
        // 检查接口是否可用
        wx.getSetting({
            success(res) {
                if (!res['scope.record']) {
                    // 接口调用询问
                    wx.authorize({
                        scope: 'scope.record',
                        success(res) {
                            const options = {
                                sampleRate: 16000,
                                frameSize: 16,
                                numberOfChannels: 1,
                                format: 'wav'
                            }
                            _this.setData({
                                'isluyin': true,
                            })
                            _this.recorderManager.start(options);
                        },
                        fail() {
                            wx.showToast({
                                title: '录音权限未开启',
                                icon: 'error'
                            })
                        },
                        complete() {}
                    })
                }
            }
        })
    },

    /*new code from now on */
    onShareAppMessage: function (res) {
        return {
            title: '天聪智能闽南话识别演示平台',
            path: '/pages/index/index'
        }
    },

    onShareTimeline: function (res) {
        return {
            title: '天聪智能闽南话识别演示平台',
            path: '/pages/index/index'
        }
    }
})