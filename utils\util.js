var MD5 = require("./md5.js")

async function bdts(from, to, content) {
  var q = content
  //注册获得appid和密匙
  var appid = '20220125001067391'
  var key = 'vKFv1vOKfNU0p1pSi8Hz'
  var salt = (new Date).getTime();
  var string = appid + q + salt + key
  var sign = MD5.MD5(string) //MD5是一个写好的加密函数，复制黏贴就好
  //传入的参数名应该是严格要求的，比如"q"改成"value"是不允许的
  var resp = () => {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
        data: {
          q,
          from,
          to,
          appid,
          salt,
          sign
        },
        success: res => {
          if (res.statusCode == 200) {
            resolve(res.data.trans_result[0].dst)
          } else {
            wx.showToast({
                icon: "none",
                title: '翻译服务异常，请使用其它语种'
              })
          }
        },
        fail: res => {
          wx.showToast({
            icon: "none",
            title: '翻译服务请求异常，请使用其它语种'
          })
          reject(res) // reject通常返回Error实例
        }
      })
    })
  }
  return await resp()
}

// 去除字符串末尾所有非中英文数字
function dealSymbol(val) {
    let re = new RegExp('^[\u4e00-\u9fa5a-zA-Z0-9]+$','i');
    while (!re.test(val.substr(-1))) {
        console.log("val: ", val)
        val = val.substr(0, val.length - 1);
    }
    return val;
}

// 英文标点转中文 标点前后加空格
function puncEn2Zh(s) { 
  s = s.replace(/:/g, '：'); // 正则  g是全局匹配
  s = s.replace(/[.]/g, '。'); // 这个也加个[]才正常点
  s = s.replace(/"/g, '“');
  s = s.replace(/"/g, '”');
  s = s.replace(/,/g, '，');
  s = s.replace(/[?]/g, '？'); // /?/g 不知道为啥不行
  s = s.replace(/,/g, '、');
  s = s.replace(/;/g, '；');
  s = s.replace(/'/g, '‘');
  s = s.replace(/'/g, '’');
  var new_s = ""
//   const puncsReg = /[：。“”，？、；‘’]/g
  for (var i = 0, len = s.length; i < len; i++) {
      new_s = new_s + s[i]
  }
  return new_s
}

function sleep(numberMillis) {
  var now = new Date();
  var exitTime = now.getTime() + numberMillis;
  while (true) {
    now = new Date();
    if (now.getTime() > exitTime)
      return;
  }
}

function formatTime(date) {
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()

  var hour = date.getHours()
  var minute = date.getMinutes()
  var second = date.getSeconds();


  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

function formatNumber(n) {
  n = n.toString()
  return n[1] ? n : '0' + n
}

const request = (url, options) => {
  console.log("url, options", url, options)
  return wx.request({
    url: url,
    method: options.method,
    data: options.data,
    header: {
      'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    success(res) {
      return options.cb_fn && options.cb_fn(res.data) //回调函数
    },
    fail(err) {
      console.log("err: ", err)
    }
  })
}


const post = (url, jsonData, cb_fn) => {
  return request(url, {
    method: 'POST',
    data: jsonData,
    cb_fn: cb_fn
  })
}

function toUtf8(str) {
  var out, i, len, c;
  out = "";
  len = str.length;
  for (i = 0; i < len; i++) {
    c = str.charCodeAt(i);
    if ((c >= 0x0001) && (c <= 0x007F)) {
      out += str.charAt(i);
    } else if (c > 0x07FF) {
      out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
      out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
    } else {
      out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
    }
  }
  return out;
}

/**
 * 使用循环的方式判断一个元素是否存在于一个数组中
 * @param {Object} arr 数组
 * @param {Object} value 元素值
 */
function isInArray(arr,value){
  for(var i = 0; i < arr.length; i++){
      if(value === arr[i]){
          return true;
      }
  }
  return false;
}

module.exports = {
  dealSymbol: dealSymbol,
  formatTime: formatTime,
  toUtf8: toUtf8,
  post: post,
  puncEn2Zh: puncEn2Zh,
  bdts: bdts,
  sleep: sleep,
  isInArray: isInArray
}